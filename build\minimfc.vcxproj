﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{49A40B0B-8836-3D82-9B39-04E69E27FA30}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <VcpkgEnabled>false</VcpkgEnabled>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>minimfc</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseOfMfc>Dynamic</UseOfMfc>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseOfMfc>Dynamic</UseOfMfc>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseOfMfc>Dynamic</UseOfMfc>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseOfMfc>Dynamic</UseOfMfc>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="do_not_import_user.props" Condition="exists('do_not_import_user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\Documents\Code\minimfc\build\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">minimfc.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">minimfc</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\Documents\Code\minimfc\build\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">minimfc.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">minimfc</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\Documents\Code\minimfc\build\MinSizeRel\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">minimfc.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">minimfc</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\Documents\Code\minimfc\build\RelWithDebInfo\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">minimfc.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">minimfc</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</GenerateManifest>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;_DEBUG;_AFXDLL;UNICODE;_UNICODE;CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;_AFXDLL;UNICODE;_UNICODE;CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message></Message>
      <Command>setlocal
C:\Windows\System32\WindowsPowerShell\v1.0\powershell.exe -noprofile -executionpolicy Bypass -file D:/Library/Vc/scripts/buildsystems/msbuild/applocal.ps1 -targetBinary D:/Documents/Code/minimfc/build/Debug/minimfc.exe -installedDir D:/Library/Vc/installed/x64-windows/debug/bin -OutVariable out
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>shell32.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/Documents/Code/minimfc/build/Debug/minimfc.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/Documents/Code/minimfc/build/Debug/minimfc.pdb</ProgramDataBaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_AFXDLL;UNICODE;_UNICODE;CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_AFXDLL;UNICODE;_UNICODE;CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message></Message>
      <Command>setlocal
C:\Windows\System32\WindowsPowerShell\v1.0\powershell.exe -noprofile -executionpolicy Bypass -file D:/Library/Vc/scripts/buildsystems/msbuild/applocal.ps1 -targetBinary D:/Documents/Code/minimfc/build/Release/minimfc.exe -installedDir D:/Library/Vc/installed/x64-windows/bin -OutVariable out
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>shell32.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/Documents/Code/minimfc/build/Release/minimfc.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/Documents/Code/minimfc/build/Release/minimfc.pdb</ProgramDataBaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MinSpace</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_AFXDLL;UNICODE;_UNICODE;CMAKE_INTDIR="MinSizeRel"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_AFXDLL;UNICODE;_UNICODE;CMAKE_INTDIR=\"MinSizeRel\"</PreprocessorDefinitions>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message></Message>
      <Command>setlocal
C:\Windows\System32\WindowsPowerShell\v1.0\powershell.exe -noprofile -executionpolicy Bypass -file D:/Library/Vc/scripts/buildsystems/msbuild/applocal.ps1 -targetBinary D:/Documents/Code/minimfc/build/MinSizeRel/minimfc.exe -installedDir D:/Library/Vc/installed/x64-windows/bin -OutVariable out
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>shell32.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/Documents/Code/minimfc/build/MinSizeRel/minimfc.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/Documents/Code/minimfc/build/MinSizeRel/minimfc.pdb</ProgramDataBaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_AFXDLL;UNICODE;_UNICODE;CMAKE_INTDIR="RelWithDebInfo"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_AFXDLL;UNICODE;_UNICODE;CMAKE_INTDIR=\"RelWithDebInfo\"</PreprocessorDefinitions>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message></Message>
      <Command>setlocal
C:\Windows\System32\WindowsPowerShell\v1.0\powershell.exe -noprofile -executionpolicy Bypass -file D:/Library/Vc/scripts/buildsystems/msbuild/applocal.ps1 -targetBinary D:/Documents/Code/minimfc/build/RelWithDebInfo/minimfc.exe -installedDir D:/Library/Vc/installed/x64-windows/bin -OutVariable out
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>shell32.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/Documents/Code/minimfc/build/RelWithDebInfo/minimfc.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/Documents/Code/minimfc/build/RelWithDebInfo/minimfc.pdb</ProgramDataBaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="D:\Documents\Code\minimfc\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule D:/Documents/Code/minimfc/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
D:\Library\CMake\bin\cmake.exe -SD:/Documents/Code/minimfc -BD:/Documents/Code/minimfc/build --check-stamp-file D:/Documents/Code/minimfc/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\Documents\Code\minimfc\build\CMakeFiles\4.0.3\CMakeCXXCompiler.cmake;D:\Documents\Code\minimfc\build\CMakeFiles\4.0.3\CMakeRCCompiler.cmake;D:\Documents\Code\minimfc\build\CMakeFiles\4.0.3\CMakeSystem.cmake;D:\Library\CMake\share\cmake-4.0\Modules\CMakeCXXInformation.cmake;D:\Library\CMake\share\cmake-4.0\Modules\CMakeCommonLanguageInclude.cmake;D:\Library\CMake\share\cmake-4.0\Modules\CMakeDependentOption.cmake;D:\Library\CMake\share\cmake-4.0\Modules\CMakeGenericSystem.cmake;D:\Library\CMake\share\cmake-4.0\Modules\CMakeInitializeConfigs.cmake;D:\Library\CMake\share\cmake-4.0\Modules\CMakeLanguageInformation.cmake;D:\Library\CMake\share\cmake-4.0\Modules\CMakeRCInformation.cmake;D:\Library\CMake\share\cmake-4.0\Modules\CMakeSystemSpecificInformation.cmake;D:\Library\CMake\share\cmake-4.0\Modules\CMakeSystemSpecificInitialize.cmake;D:\Library\CMake\share\cmake-4.0\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\Library\CMake\share\cmake-4.0\Modules\Compiler\MSVC-CXX.cmake;D:\Library\CMake\share\cmake-4.0\Modules\Compiler\MSVC.cmake;D:\Library\CMake\share\cmake-4.0\Modules\Internal\CMakeCXXLinkerInformation.cmake;D:\Library\CMake\share\cmake-4.0\Modules\Internal\CMakeCommonLinkerInformation.cmake;D:\Library\CMake\share\cmake-4.0\Modules\Linker\MSVC-CXX.cmake;D:\Library\CMake\share\cmake-4.0\Modules\Linker\MSVC.cmake;D:\Library\CMake\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;D:\Library\CMake\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC.cmake;D:\Library\CMake\share\cmake-4.0\Modules\Platform\Windows-Initialize.cmake;D:\Library\CMake\share\cmake-4.0\Modules\Platform\Windows-MSVC-CXX.cmake;D:\Library\CMake\share\cmake-4.0\Modules\Platform\Windows-MSVC.cmake;D:\Library\CMake\share\cmake-4.0\Modules\Platform\Windows.cmake;D:\Library\CMake\share\cmake-4.0\Modules\Platform\WindowsPaths.cmake;D:\Library\Vc\scripts\buildsystems\vcpkg.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\Documents\Code\minimfc\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule D:/Documents/Code/minimfc/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
D:\Library\CMake\bin\cmake.exe -SD:/Documents/Code/minimfc -BD:/Documents/Code/minimfc/build --check-stamp-file D:/Documents/Code/minimfc/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\Documents\Code\minimfc\build\CMakeFiles\4.0.3\CMakeCXXCompiler.cmake;D:\Documents\Code\minimfc\build\CMakeFiles\4.0.3\CMakeRCCompiler.cmake;D:\Documents\Code\minimfc\build\CMakeFiles\4.0.3\CMakeSystem.cmake;D:\Library\CMake\share\cmake-4.0\Modules\CMakeCXXInformation.cmake;D:\Library\CMake\share\cmake-4.0\Modules\CMakeCommonLanguageInclude.cmake;D:\Library\CMake\share\cmake-4.0\Modules\CMakeDependentOption.cmake;D:\Library\CMake\share\cmake-4.0\Modules\CMakeGenericSystem.cmake;D:\Library\CMake\share\cmake-4.0\Modules\CMakeInitializeConfigs.cmake;D:\Library\CMake\share\cmake-4.0\Modules\CMakeLanguageInformation.cmake;D:\Library\CMake\share\cmake-4.0\Modules\CMakeRCInformation.cmake;D:\Library\CMake\share\cmake-4.0\Modules\CMakeSystemSpecificInformation.cmake;D:\Library\CMake\share\cmake-4.0\Modules\CMakeSystemSpecificInitialize.cmake;D:\Library\CMake\share\cmake-4.0\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\Library\CMake\share\cmake-4.0\Modules\Compiler\MSVC-CXX.cmake;D:\Library\CMake\share\cmake-4.0\Modules\Compiler\MSVC.cmake;D:\Library\CMake\share\cmake-4.0\Modules\Internal\CMakeCXXLinkerInformation.cmake;D:\Library\CMake\share\cmake-4.0\Modules\Internal\CMakeCommonLinkerInformation.cmake;D:\Library\CMake\share\cmake-4.0\Modules\Linker\MSVC-CXX.cmake;D:\Library\CMake\share\cmake-4.0\Modules\Linker\MSVC.cmake;D:\Library\CMake\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;D:\Library\CMake\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC.cmake;D:\Library\CMake\share\cmake-4.0\Modules\Platform\Windows-Initialize.cmake;D:\Library\CMake\share\cmake-4.0\Modules\Platform\Windows-MSVC-CXX.cmake;D:\Library\CMake\share\cmake-4.0\Modules\Platform\Windows-MSVC.cmake;D:\Library\CMake\share\cmake-4.0\Modules\Platform\Windows.cmake;D:\Library\CMake\share\cmake-4.0\Modules\Platform\WindowsPaths.cmake;D:\Library\Vc\scripts\buildsystems\vcpkg.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\Documents\Code\minimfc\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule D:/Documents/Code/minimfc/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
D:\Library\CMake\bin\cmake.exe -SD:/Documents/Code/minimfc -BD:/Documents/Code/minimfc/build --check-stamp-file D:/Documents/Code/minimfc/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\Documents\Code\minimfc\build\CMakeFiles\4.0.3\CMakeCXXCompiler.cmake;D:\Documents\Code\minimfc\build\CMakeFiles\4.0.3\CMakeRCCompiler.cmake;D:\Documents\Code\minimfc\build\CMakeFiles\4.0.3\CMakeSystem.cmake;D:\Library\CMake\share\cmake-4.0\Modules\CMakeCXXInformation.cmake;D:\Library\CMake\share\cmake-4.0\Modules\CMakeCommonLanguageInclude.cmake;D:\Library\CMake\share\cmake-4.0\Modules\CMakeDependentOption.cmake;D:\Library\CMake\share\cmake-4.0\Modules\CMakeGenericSystem.cmake;D:\Library\CMake\share\cmake-4.0\Modules\CMakeInitializeConfigs.cmake;D:\Library\CMake\share\cmake-4.0\Modules\CMakeLanguageInformation.cmake;D:\Library\CMake\share\cmake-4.0\Modules\CMakeRCInformation.cmake;D:\Library\CMake\share\cmake-4.0\Modules\CMakeSystemSpecificInformation.cmake;D:\Library\CMake\share\cmake-4.0\Modules\CMakeSystemSpecificInitialize.cmake;D:\Library\CMake\share\cmake-4.0\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\Library\CMake\share\cmake-4.0\Modules\Compiler\MSVC-CXX.cmake;D:\Library\CMake\share\cmake-4.0\Modules\Compiler\MSVC.cmake;D:\Library\CMake\share\cmake-4.0\Modules\Internal\CMakeCXXLinkerInformation.cmake;D:\Library\CMake\share\cmake-4.0\Modules\Internal\CMakeCommonLinkerInformation.cmake;D:\Library\CMake\share\cmake-4.0\Modules\Linker\MSVC-CXX.cmake;D:\Library\CMake\share\cmake-4.0\Modules\Linker\MSVC.cmake;D:\Library\CMake\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;D:\Library\CMake\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC.cmake;D:\Library\CMake\share\cmake-4.0\Modules\Platform\Windows-Initialize.cmake;D:\Library\CMake\share\cmake-4.0\Modules\Platform\Windows-MSVC-CXX.cmake;D:\Library\CMake\share\cmake-4.0\Modules\Platform\Windows-MSVC.cmake;D:\Library\CMake\share\cmake-4.0\Modules\Platform\Windows.cmake;D:\Library\CMake\share\cmake-4.0\Modules\Platform\WindowsPaths.cmake;D:\Library\Vc\scripts\buildsystems\vcpkg.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\Documents\Code\minimfc\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule D:/Documents/Code/minimfc/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
D:\Library\CMake\bin\cmake.exe -SD:/Documents/Code/minimfc -BD:/Documents/Code/minimfc/build --check-stamp-file D:/Documents/Code/minimfc/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\Documents\Code\minimfc\build\CMakeFiles\4.0.3\CMakeCXXCompiler.cmake;D:\Documents\Code\minimfc\build\CMakeFiles\4.0.3\CMakeRCCompiler.cmake;D:\Documents\Code\minimfc\build\CMakeFiles\4.0.3\CMakeSystem.cmake;D:\Library\CMake\share\cmake-4.0\Modules\CMakeCXXInformation.cmake;D:\Library\CMake\share\cmake-4.0\Modules\CMakeCommonLanguageInclude.cmake;D:\Library\CMake\share\cmake-4.0\Modules\CMakeDependentOption.cmake;D:\Library\CMake\share\cmake-4.0\Modules\CMakeGenericSystem.cmake;D:\Library\CMake\share\cmake-4.0\Modules\CMakeInitializeConfigs.cmake;D:\Library\CMake\share\cmake-4.0\Modules\CMakeLanguageInformation.cmake;D:\Library\CMake\share\cmake-4.0\Modules\CMakeRCInformation.cmake;D:\Library\CMake\share\cmake-4.0\Modules\CMakeSystemSpecificInformation.cmake;D:\Library\CMake\share\cmake-4.0\Modules\CMakeSystemSpecificInitialize.cmake;D:\Library\CMake\share\cmake-4.0\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\Library\CMake\share\cmake-4.0\Modules\Compiler\MSVC-CXX.cmake;D:\Library\CMake\share\cmake-4.0\Modules\Compiler\MSVC.cmake;D:\Library\CMake\share\cmake-4.0\Modules\Internal\CMakeCXXLinkerInformation.cmake;D:\Library\CMake\share\cmake-4.0\Modules\Internal\CMakeCommonLinkerInformation.cmake;D:\Library\CMake\share\cmake-4.0\Modules\Linker\MSVC-CXX.cmake;D:\Library\CMake\share\cmake-4.0\Modules\Linker\MSVC.cmake;D:\Library\CMake\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;D:\Library\CMake\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC.cmake;D:\Library\CMake\share\cmake-4.0\Modules\Platform\Windows-Initialize.cmake;D:\Library\CMake\share\cmake-4.0\Modules\Platform\Windows-MSVC-CXX.cmake;D:\Library\CMake\share\cmake-4.0\Modules\Platform\Windows-MSVC.cmake;D:\Library\CMake\share\cmake-4.0\Modules\Platform\Windows.cmake;D:\Library\CMake\share\cmake-4.0\Modules\Platform\WindowsPaths.cmake;D:\Library\Vc\scripts\buildsystems\vcpkg.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\Documents\Code\minimfc\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="D:\Documents\Code\minimfc\main.cpp" />
    <ClInclude Include="D:\Documents\Code\minimfc\resource.h" />
    <ResourceCompile Include="D:\Documents\Code\minimfc\main.rc" />
    <None Include="D:\Documents\Code\minimfc\app.ico">
    </None>
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <ProjectReference Include="D:\Documents\Code\minimfc\build\ZERO_CHECK.vcxproj">
      <Project>{9257DA38-491D-3C22-8B33-4B48A159A6FD}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>