#include "resource.h"
#include "winver.h"

ID_MENU MENU
BEGIN
    POPUP "Main"
    BEGIN
        MENUITEM "Quit", ID_MENU_QUIT
    END
END
    
MAIN_ICON ICON "app.ico"
    
VS_VERSION_INFO VERSIONINFO
    FILEVERSION 1,0,0,1
    PRODUCTVERSION 1,0,0,1
    FILEFLAGSMASK 0x3fL
#ifdef _DEBUG
    FILEFLAGS 0x1L
#else
    FILEFLAGS 0x0L
#endif
    FILEOS 0x4L
    FILETYPE 0x1L
    FILESUBTYPE 0x0L
BEGIN
    BLOCK "StringFileInfo"
    BEGIN
        BLOCK "040904b0"
        BEGIN
            VALUE "CompanyName", "GoG.one\0"
            VALUE "FileDescription", "MFC Application\0"
            VALUE "FileVersion", "*******\0"
            VALUE "InternalName", "MFC Application\0"
            VALUE "LegalCopyright", "Copyright (c) 2023 GoG.one\0"
            VALUE "OriginalFilename", "minimfc.exe\0"
            VALUE "ProductName", "MFC Application\0"
            VALUE "ProductVersion", "*******\0"
        END
    END
    BLOCK "VarFileInfo"
    BEGIN
        VALUE "Translation", 0x409, 1200
    END
END
