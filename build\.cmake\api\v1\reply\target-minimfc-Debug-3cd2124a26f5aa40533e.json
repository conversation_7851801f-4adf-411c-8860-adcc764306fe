{"artifacts": [{"path": "Debug/minimfc.exe"}, {"path": "Debug/minimfc.pdb"}], "backtrace": 2, "backtraceGraph": {"commands": ["_add_executable", "add_executable", "target_link_libraries", "add_definitions", "target_compile_definitions"], "files": ["D:/Library/Vc/scripts/buildsystems/vcpkg.cmake", "CMakeLists.txt"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 22, "parent": 0}, {"command": 0, "file": 0, "line": 600, "parent": 1}, {"command": 2, "file": 1, "line": 35, "parent": 0}, {"command": 3, "file": 1, "line": 19, "parent": 0}, {"command": 4, "file": 1, "line": 38, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd"}], "defines": [{"backtrace": 4, "define": "UNICODE"}, {"backtrace": 5, "define": "WIN32"}, {"backtrace": 5, "define": "_AFXDLL"}, {"backtrace": 5, "define": "_DEBUG"}, {"backtrace": 4, "define": "_UNICODE"}, {"backtrace": 5, "define": "_WINDOWS"}], "language": "CXX", "languageStandard": {"backtraces": [2], "standard": "17"}, "sourceIndexes": [0]}, {"compileCommandFragments": [{"fragment": "-DWIN32 -D_DEBUG"}], "defines": [{"backtrace": 4, "define": "UNICODE"}, {"backtrace": 5, "define": "WIN32"}, {"backtrace": 5, "define": "_AFXDLL"}, {"backtrace": 5, "define": "_DEBUG"}, {"backtrace": 4, "define": "_UNICODE"}, {"backtrace": 5, "define": "_WINDOWS"}], "language": "RC", "sourceIndexes": [2]}], "dependencies": [{"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "id": "minimfc::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -MDd", "role": "flags"}, {"fragment": "/machine:x64 /debug /INCREMENTAL", "role": "flags"}, {"fragment": "/subsystem:windows", "role": "flags"}, {"backtrace": 3, "fragment": "shell32.lib", "role": "libraries"}, {"fragment": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib", "role": "libraries"}], "language": "CXX"}, "name": "minimfc", "nameOnDisk": "minimfc.exe", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 2]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [1]}, {"name": "", "sourceIndexes": [3]}], "sources": [{"backtrace": 2, "compileGroupIndex": 0, "path": "main.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "resource.h", "sourceGroupIndex": 1}, {"backtrace": 2, "compileGroupIndex": 1, "path": "main.rc", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "app.ico", "sourceGroupIndex": 2}], "type": "EXECUTABLE"}