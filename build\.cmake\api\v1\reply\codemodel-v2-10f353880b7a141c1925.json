{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-Debug-b771b787856e8c35f0be.json", "minimumCMakeVersion": {"string": "3.20"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "minimfc", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-Debug-3a6d81f38eed11a0fc57.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-Debug-98ee960a34d77f1e5665.json", "name": "ZERO_CHECK", "projectIndex": 0}, {"directoryIndex": 0, "id": "minimfc::@6890427a1f51a3e7e1df", "jsonFile": "target-minimfc-Debug-c981031e071aec38830e.json", "name": "minimfc", "projectIndex": 0}]}, {"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-Release-13406bcea8cd3ca433b2.json", "minimumCMakeVersion": {"string": "3.20"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}], "name": "Release", "projects": [{"directoryIndexes": [0], "name": "minimfc", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-Release-3a6d81f38eed11a0fc57.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-Release-98ee960a34d77f1e5665.json", "name": "ZERO_CHECK", "projectIndex": 0}, {"directoryIndex": 0, "id": "minimfc::@6890427a1f51a3e7e1df", "jsonFile": "target-minimfc-Release-03d4312d3f6b826cf95c.json", "name": "minimfc", "projectIndex": 0}]}, {"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-MinSizeRel-9045170a32d0495b8333.json", "minimumCMakeVersion": {"string": "3.20"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}], "name": "MinSizeRel", "projects": [{"directoryIndexes": [0], "name": "minimfc", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-MinSizeRel-3a6d81f38eed11a0fc57.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-MinSizeRel-98ee960a34d77f1e5665.json", "name": "ZERO_CHECK", "projectIndex": 0}, {"directoryIndex": 0, "id": "minimfc::@6890427a1f51a3e7e1df", "jsonFile": "target-minimfc-MinSizeRel-28f855f0b1e1a8f3ff3d.json", "name": "minimfc", "projectIndex": 0}]}, {"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-RelWithDebInfo-7d7ae6a4455a44a98e6d.json", "minimumCMakeVersion": {"string": "3.20"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}], "name": "RelWithDebInfo", "projects": [{"directoryIndexes": [0], "name": "minimfc", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-3a6d81f38eed11a0fc57.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-RelWithDebInfo-98ee960a34d77f1e5665.json", "name": "ZERO_CHECK", "projectIndex": 0}, {"directoryIndex": 0, "id": "minimfc::@6890427a1f51a3e7e1df", "jsonFile": "target-minimfc-RelWithDebInfo-45ca80ab91596e97b942.json", "name": "minimfc", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "D:/Documents/Code/minimfc/build", "source": "D:/Documents/Code/minimfc"}, "version": {"major": 2, "minor": 8}}