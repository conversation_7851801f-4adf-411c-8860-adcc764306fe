cmake_minimum_required(VERSION 3.20)

# 设置项目名称和版本
project(minimfc VERSION 1.0.0.0 LANGUAGES CXX RC)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 确保这是Windows平台
if(NOT WIN32)
    message(FATAL_ERROR "This project is designed for Windows only")
endif()

# 设置MFC使用方式 (Shared/Static)
set(CMAKE_MFC_FLAG 2)  # 2 = Shared MFC, 1 = Static MFC

# 设置字符集为Unicode
add_definitions(-DUNICODE -D_UNICODE)

# 设置Windows目标版本
add_definitions(-DWINVER=0x0A00 -D_WIN32_WINNT=0x0A00)

# 配置编译器选项
if(MSVC)
    # 设置运行时库
    set(CMAKE_MSVC_RUNTIME_LIBRARY "MultiThreaded$<$<CONFIG:Debug>:Debug>DLL")
    
    # 设置警告级别
    add_compile_options(/W3)
    
    # Debug配置
    set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} /MDd /Zi /Od")
    set(CMAKE_EXE_LINKER_FLAGS_DEBUG "${CMAKE_EXE_LINKER_FLAGS_DEBUG} /DEBUG")
    
    # Release配置
    set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} /MD /O2")
    set(CMAKE_EXE_LINKER_FLAGS_RELEASE "${CMAKE_EXE_LINKER_FLAGS_RELEASE} /SUBSYSTEM:WINDOWS")
endif()

# 源文件
set(SOURCES
    main.cpp
)

# 头文件
set(HEADERS
    resource.h
)

# 资源文件
set(RESOURCES
    main.rc
)

# 图标文件
set(ICONS
    app.ico
)

# 创建可执行文件
add_executable(${PROJECT_NAME} WIN32
    ${SOURCES}
    ${HEADERS}
    ${RESOURCES}
    ${ICONS}
)

# 设置目标属性
set_target_properties(${PROJECT_NAME} PROPERTIES
    OUTPUT_NAME "minimfc"
    WIN32_EXECUTABLE TRUE
)

# 链接MFC库
target_link_libraries(${PROJECT_NAME}
    PRIVATE
    # MFC会自动链接，但我们可以显式指定一些系统库
    shell32
    user32
    gdi32
    kernel32
    comctl32
)

# 设置预处理器定义
target_compile_definitions(${PROJECT_NAME} PRIVATE
    $<$<CONFIG:Debug>:_DEBUG>
    $<$<CONFIG:Release>:NDEBUG>
    WIN32
    _WINDOWS
    _AFXDLL  # 使用MFC DLL
)

# 设置包含目录
target_include_directories(${PROJECT_NAME} PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}
)

# 配置安装
install(TARGETS ${PROJECT_NAME}
    RUNTIME DESTINATION bin
)

# 安装图标文件
install(FILES app.ico
    DESTINATION bin
)

# 设置启动项目（如果在Visual Studio中使用）
set_property(DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR} PROPERTY VS_STARTUP_PROJECT ${PROJECT_NAME})

# 设置Visual Studio的工作目录
set_target_properties(${PROJECT_NAME} PROPERTIES
    VS_DEBUGGER_WORKING_DIRECTORY "${CMAKE_CURRENT_SOURCE_DIR}"
)

# 配置不同的构建类型
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release CACHE STRING "Choose the type of build" FORCE)
    set_property(CACHE CMAKE_BUILD_TYPE PROPERTY STRINGS "Debug" "Release" "MinSizeRel" "RelWithDebInfo")
endif()

# 打印配置信息
message(STATUS "Project: ${PROJECT_NAME}")
message(STATUS "Version: ${PROJECT_VERSION}")
message(STATUS "Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "MFC Flag: ${CMAKE_MFC_FLAG}")
message(STATUS "C++ Standard: ${CMAKE_CXX_STANDARD}")
