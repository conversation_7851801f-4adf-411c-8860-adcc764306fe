{"inputs": [{"path": "CMakeLists.txt"}, {"isGenerated": true, "path": "build/CMakeFiles/4.0.3/CMakeSystem.cmake"}, {"isExternal": true, "path": "D:/Library/Vc/scripts/buildsystems/vcpkg.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Library/CMake/share/cmake-4.0/Modules/CMakeDependentOption.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Library/CMake/share/cmake-4.0/Modules/CMakeSystemSpecificInitialize.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Library/CMake/share/cmake-4.0/Modules/Platform/Windows-Initialize.cmake"}, {"isGenerated": true, "path": "build/CMakeFiles/4.0.3/CMakeCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Library/CMake/share/cmake-4.0/Modules/CMakeSystemSpecificInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Library/CMake/share/cmake-4.0/Modules/CMakeGenericSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Library/CMake/share/cmake-4.0/Modules/CMakeInitializeConfigs.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Library/CMake/share/cmake-4.0/Modules/Platform/Windows.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Library/CMake/share/cmake-4.0/Modules/Platform/WindowsPaths.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Library/CMake/share/cmake-4.0/Modules/CMakeCXXInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Library/CMake/share/cmake-4.0/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Library/CMake/share/cmake-4.0/Modules/Compiler/MSVC-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Library/CMake/share/cmake-4.0/Modules/Compiler/MSVC.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Library/CMake/share/cmake-4.0/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Library/CMake/share/cmake-4.0/Modules/Platform/Windows-MSVC-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Library/CMake/share/cmake-4.0/Modules/Platform/Windows-MSVC.cmake"}, {"isGenerated": true, "path": "build/CMakeFiles/4.0.3/CMakeRCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Library/CMake/share/cmake-4.0/Modules/CMakeRCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Library/CMake/share/cmake-4.0/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Library/CMake/share/cmake-4.0/Modules/Internal/CMakeCXXLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Library/CMake/share/cmake-4.0/Modules/Internal/CMakeCommonLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Library/CMake/share/cmake-4.0/Modules/Linker/MSVC-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Library/CMake/share/cmake-4.0/Modules/Linker/MSVC.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Library/CMake/share/cmake-4.0/Modules/Platform/Linker/Windows-MSVC-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Library/CMake/share/cmake-4.0/Modules/Platform/Linker/Windows-MSVC.cmake"}], "kind": "cmakeFiles", "paths": {"build": "D:/Documents/Code/minimfc/build", "source": "D:/Documents/Code/minimfc"}, "version": {"major": 1, "minor": 1}}