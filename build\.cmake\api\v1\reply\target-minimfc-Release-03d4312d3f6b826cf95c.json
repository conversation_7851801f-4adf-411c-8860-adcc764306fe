{"artifacts": [{"path": "Release/minimfc.exe"}, {"path": "Release/minimfc.pdb"}], "backtrace": 2, "backtraceGraph": {"commands": ["_add_executable", "add_executable", "install", "target_link_libraries", "add_compile_options", "target_compile_definitions", "add_definitions", "target_include_directories"], "files": ["D:/Library/Vc/scripts/buildsystems/vcpkg.cmake", "CMakeLists.txt"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 62, "parent": 0}, {"command": 0, "file": 0, "line": 600, "parent": 1}, {"command": 2, "file": 1, "line": 101, "parent": 0}, {"command": 3, "file": 1, "line": 76, "parent": 0}, {"command": 4, "file": 1, "line": 30, "parent": 0}, {"command": 5, "file": 1, "line": 87, "parent": 0}, {"command": 6, "file": 1, "line": 19, "parent": 0}, {"command": 6, "file": 1, "line": 22, "parent": 0}, {"command": 7, "file": 1, "line": 96, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG /MD /O2 -std:c++17 -MD"}, {"backtrace": 5, "fragment": "/W3"}], "defines": [{"backtrace": 6, "define": "NDEBUG"}, {"backtrace": 7, "define": "UNICODE"}, {"backtrace": 6, "define": "WIN32"}, {"backtrace": 8, "define": "WINVER=0x0A00"}, {"backtrace": 6, "define": "_AFXDLL"}, {"backtrace": 7, "define": "_UNICODE"}, {"backtrace": 8, "define": "_WIN32_WINNT=0x0A00"}, {"backtrace": 6, "define": "_WINDOWS"}], "includes": [{"backtrace": 9, "path": "D:/Documents/Code/minimfc"}], "language": "CXX", "languageStandard": {"backtraces": [2], "standard": "17"}, "sourceIndexes": [0]}, {"compileCommandFragments": [{"fragment": "-DWIN32"}], "defines": [{"backtrace": 6, "define": "NDEBUG"}, {"backtrace": 7, "define": "UNICODE"}, {"backtrace": 6, "define": "WIN32"}, {"backtrace": 8, "define": "WINVER=0x0A00"}, {"backtrace": 6, "define": "_AFXDLL"}, {"backtrace": 7, "define": "_UNICODE"}, {"backtrace": 8, "define": "_WIN32_WINNT=0x0A00"}, {"backtrace": 6, "define": "_WINDOWS"}], "includes": [{"backtrace": 9, "path": "D:/Documents/Code/minimfc"}], "language": "RC", "sourceIndexes": [2]}], "debugger": {"workingDirectory": "D:/Documents/Code/minimfc"}, "dependencies": [{"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "id": "minimfc::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 3, "path": "bin"}], "prefix": {"path": "C:/Program Files/minimfc"}}, "link": {"commandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG /MD /O2 -MD", "role": "flags"}, {"fragment": "/machine:x64 /INCREMENTAL:NO /SUBSYSTEM:WINDOWS", "role": "flags"}, {"fragment": "/subsystem:windows", "role": "flags"}, {"backtrace": 4, "fragment": "shell32.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "user32.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "gdi32.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "kernel32.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "comctl32.lib", "role": "libraries"}, {"fragment": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib", "role": "libraries"}], "language": "CXX"}, "name": "minimfc", "nameOnDisk": "minimfc.exe", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 2]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [1]}, {"name": "", "sourceIndexes": [3]}], "sources": [{"backtrace": 2, "compileGroupIndex": 0, "path": "main.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "resource.h", "sourceGroupIndex": 1}, {"backtrace": 2, "compileGroupIndex": 1, "path": "main.rc", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "app.ico", "sourceGroupIndex": 2}], "type": "EXECUTABLE"}