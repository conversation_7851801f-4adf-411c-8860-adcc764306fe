{"entries": [{"name": "CMAKE_AR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "C:/Program Files/Microsoft Visual Studio/2022/Professional/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/lib.exe"}, {"name": "CMAKE_BUILD_TYPE", "properties": [{"name": "HELPSTRING", "value": "Choose the type of build"}, {"name": "STRINGS", "value": "Debug;Release;MinSizeRel;RelWithDebInfo"}], "type": "STRING", "value": "Release"}, {"name": "CMAKE_CACHEFILE_DIR", "properties": [{"name": "HELPSTRING", "value": "This is the directory where this CMakeCache.txt was created"}], "type": "INTERNAL", "value": "d:/Documents/Code/minimfc/build"}, {"name": "CMAKE_CACHE_MAJOR_VERSION", "properties": [{"name": "HELPSTRING", "value": "Major version of cmake used to create the current loaded cache"}], "type": "INTERNAL", "value": "4"}, {"name": "CMAKE_CACHE_MINOR_VERSION", "properties": [{"name": "HELPSTRING", "value": "Minor version of cmake used to create the current loaded cache"}], "type": "INTERNAL", "value": "0"}, {"name": "CMAKE_CACHE_PATCH_VERSION", "properties": [{"name": "HELPSTRING", "value": "Patch version of cmake used to create the current loaded cache"}], "type": "INTERNAL", "value": "3"}, {"name": "CMAKE_COMMAND", "properties": [{"name": "HELPSTRING", "value": "Path to CMake executable."}], "type": "INTERNAL", "value": "D:/Library/CMake/bin/cmake.exe"}, {"name": "CMAKE_CONFIGURATION_TYPES", "properties": [{"name": "HELPSTRING", "value": "Semicolon separated list of supported configuration types, only supports Debug, Release, MinSizeRel, and RelWithDebInfo, anything else will be ignored."}], "type": "STRING", "value": "Debug;Release;MinSizeRel;RelWithDebInfo"}, {"name": "CMAKE_CPACK_COMMAND", "properties": [{"name": "HELPSTRING", "value": "Path to cpack program executable."}], "type": "INTERNAL", "value": "D:/Library/CMake/bin/cpack.exe"}, {"name": "CMAKE_CTEST_COMMAND", "properties": [{"name": "HELPSTRING", "value": "Path to ctest program executable."}], "type": "INTERNAL", "value": "D:/Library/CMake/bin/ctest.exe"}, {"name": "CMAKE_CXX_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the CXX compiler during all build types."}], "type": "STRING", "value": "/DWIN32 /D_WINDOWS /EHsc"}, {"name": "CMAKE_CXX_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the CXX compiler during DEBUG builds."}], "type": "STRING", "value": "/Zi /Ob0 /Od /RTC1"}, {"name": "CMAKE_CXX_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the CXX compiler during MINSIZEREL builds."}], "type": "STRING", "value": "/O1 /Ob1 /DNDEBUG"}, {"name": "CMAKE_CXX_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the CXX compiler during RELEASE builds."}], "type": "STRING", "value": "/O2 /Ob2 /DNDEBUG"}, {"name": "CMAKE_CXX_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the CXX compiler during RELWITHDEBINFO builds."}], "type": "STRING", "value": "/Zi /O2 /Ob1 /DNDEBUG"}, {"name": "CMAKE_CXX_STANDARD_LIBRARIES", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Libraries linked by default with all C++ applications."}], "type": "STRING", "value": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib"}, {"name": "CMAKE_EXECUTABLE_FORMAT", "properties": [{"name": "HELPSTRING", "value": "Executable file format"}], "type": "INTERNAL", "value": "Unknown"}, {"name": "CMAKE_EXE_LINKER_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during all build types."}], "type": "STRING", "value": "/machine:x64"}, {"name": "CMAKE_EXE_LINKER_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during DEBUG builds."}], "type": "STRING", "value": "/debug /INCREMENTAL"}, {"name": "CMAKE_EXE_LINKER_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during MINSIZEREL builds."}], "type": "STRING", "value": "/INCREMENTAL:NO"}, {"name": "CMAKE_EXE_LINKER_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during RELEASE builds."}], "type": "STRING", "value": "/INCREMENTAL:NO"}, {"name": "CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during RELWITHDEBINFO builds."}], "type": "STRING", "value": "/debug /INCREMENTAL"}, {"name": "CMAKE_EXPORT_COMPILE_COMMANDS", "properties": [{"name": "HELPSTRING", "value": "No help, variable specified on the command line."}], "type": "BOOL", "value": "TRUE"}, {"name": "CMAKE_EXTRA_GENERATOR", "properties": [{"name": "HELPSTRING", "value": "Name of external makefile project generator."}], "type": "INTERNAL", "value": ""}, {"name": "CMAKE_FIND_PACKAGE_REDIRECTS_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake."}], "type": "STATIC", "value": "D:/Documents/Code/minimfc/build/CMakeFiles/pkgRedirects"}, {"name": "CMAKE_GENERATOR", "properties": [{"name": "HELPSTRING", "value": "Name of generator."}], "type": "INTERNAL", "value": "Visual Studio 17 2022"}, {"name": "CMAKE_GENERATOR_INSTANCE", "properties": [{"name": "HELPSTRING", "value": "Generator instance identifier."}], "type": "INTERNAL", "value": "C:/Program Files/Microsoft Visual Studio/2022/Professional"}, {"name": "CMAKE_GENERATOR_PLATFORM", "properties": [{"name": "HELPSTRING", "value": "Name of generator platform."}], "type": "INTERNAL", "value": "x64"}, {"name": "CMAKE_GENERATOR_TOOLSET", "properties": [{"name": "HELPSTRING", "value": "Name of generator toolset."}], "type": "INTERNAL", "value": "host=x64"}, {"name": "CMAKE_HOME_DIRECTORY", "properties": [{"name": "HELPSTRING", "value": "Source directory with the top level CMakeLists.txt file for this project"}], "type": "INTERNAL", "value": "D:/Documents/Code/minimfc"}, {"name": "CMAKE_INSTALL_PREFIX", "properties": [{"name": "HELPSTRING", "value": "Install path prefix, prepended onto install directories."}], "type": "PATH", "value": "C:/Program Files/minimfc"}, {"name": "CMAKE_LINKER", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "C:/Program Files/Microsoft Visual Studio/2022/Professional/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/link.exe"}, {"name": "CMAKE_LIST_FILE_NAME", "properties": [{"name": "HELPSTRING", "value": "Name of CMakeLists files to read"}], "type": "INTERNAL", "value": "CMakeLists.txt"}, {"name": "CMAKE_MODULE_LINKER_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during all build types."}], "type": "STRING", "value": "/machine:x64"}, {"name": "CMAKE_MODULE_LINKER_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during DEBUG builds."}], "type": "STRING", "value": "/debug /INCREMENTAL"}, {"name": "CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during MINSIZEREL builds."}], "type": "STRING", "value": "/INCREMENTAL:NO"}, {"name": "CMAKE_MODULE_LINKER_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during RELEASE builds."}], "type": "STRING", "value": "/INCREMENTAL:NO"}, {"name": "CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during RELWITHDEBINFO builds."}], "type": "STRING", "value": "/debug /INCREMENTAL"}, {"name": "CMAKE_MT", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "C:/Program Files (x86)/Windows Kits/10/bin/10.0.26100.0/x64/mt.exe"}, {"name": "CMAKE_NUMBER_OF_MAKEFILES", "properties": [{"name": "HELPSTRING", "value": "number of local generators"}], "type": "INTERNAL", "value": "1"}, {"name": "CMAKE_PLATFORM_INFO_INITIALIZED", "properties": [{"name": "HELPSTRING", "value": "Platform information initialized"}], "type": "INTERNAL", "value": "1"}, {"name": "CMAKE_PROJECT_DESCRIPTION", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": ""}, {"name": "CMAKE_PROJECT_HOMEPAGE_URL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": ""}, {"name": "CMAKE_PROJECT_NAME", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "minimfc"}, {"name": "CMAKE_PROJECT_VERSION", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "*******"}, {"name": "CMAKE_PROJECT_VERSION_MAJOR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "1"}, {"name": "CMAKE_PROJECT_VERSION_MINOR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "0"}, {"name": "CMAKE_PROJECT_VERSION_PATCH", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "0"}, {"name": "CMAKE_PROJECT_VERSION_TWEAK", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "0"}, {"name": "CMAKE_RANLIB", "properties": [{"name": "HELPSTRING", "value": "noop for ranlib"}], "type": "INTERNAL", "value": ":"}, {"name": "CMAKE_RC_COMPILER", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "RC compiler"}], "type": "FILEPATH", "value": "C:/Program Files (x86)/Windows Kits/10/bin/10.0.26100.0/x64/rc.exe"}, {"name": "CMAKE_RC_COMPILER_WORKS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "1"}, {"name": "CMAKE_RC_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags for Windows Resource Compiler during all build types."}], "type": "STRING", "value": "-DWIN32"}, {"name": "CMAKE_RC_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags for Windows Resource Compiler during DEBUG builds."}], "type": "STRING", "value": "-D_DEBUG"}, {"name": "CMAKE_RC_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags for Windows Resource Compiler during MINSIZEREL builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_RC_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags for Windows Resource Compiler during RELEASE builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_RC_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags for Windows Resource Compiler during RELWITHDEBINFO builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_ROOT", "properties": [{"name": "HELPSTRING", "value": "Path to CMake installation."}], "type": "INTERNAL", "value": "D:/Library/CMake/share/cmake-4.0"}, {"name": "CMAKE_SHARED_LINKER_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of shared libraries during all build types."}], "type": "STRING", "value": "/machine:x64"}, {"name": "CMAKE_SHARED_LINKER_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of shared libraries during DEBUG builds."}], "type": "STRING", "value": "/debug /INCREMENTAL"}, {"name": "CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of shared libraries during MINSIZEREL builds."}], "type": "STRING", "value": "/INCREMENTAL:NO"}, {"name": "CMAKE_SHARED_LINKER_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of shared libraries during RELEASE builds."}], "type": "STRING", "value": "/INCREMENTAL:NO"}, {"name": "CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of shared libraries during RELWITHDEBINFO builds."}], "type": "STRING", "value": "/debug /INCREMENTAL"}, {"name": "CMAKE_SKIP_INSTALL_RPATH", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "If set, runtime paths are not added when installing shared libraries, but are added when building."}], "type": "BOOL", "value": "NO"}, {"name": "CMAKE_SKIP_RPATH", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "If set, runtime paths are not added when using shared libraries."}], "type": "BOOL", "value": "NO"}, {"name": "CMAKE_STATIC_LINKER_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of static libraries during all build types."}], "type": "STRING", "value": "/machine:x64"}, {"name": "CMAKE_STATIC_LINKER_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of static libraries during DEBUG builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of static libraries during MINSIZEREL builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_STATIC_LINKER_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of static libraries during RELEASE builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of static libraries during RELWITHDEBINFO builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_TOOLCHAIN_FILE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "No help, variable specified on the command line."}], "type": "STRING", "value": "D:/Library/Vc/scripts/buildsystems/vcpkg.cmake"}, {"name": "CMAKE_VERBOSE_MAKEFILE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "If this value is on, makefiles will be generated without the .SILENT directive, and all commands will be echoed to the console during the make.  This is useful for debugging only. With Visual Studio IDE projects all commands are done without /nologo."}], "type": "BOOL", "value": "FALSE"}, {"name": "VCPKG_APPLOCAL_DEPS", "properties": [{"name": "HELPSTRING", "value": "Automatically copy dependencies into the output directory for executables."}], "type": "BOOL", "value": "ON"}, {"name": "VCPKG_INSTALLED_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory which contains the installed libraries for each triplet"}], "type": "PATH", "value": "D:/Library/Vc/installed"}, {"name": "VCPKG_MANIFEST_DIR", "properties": [{"name": "HELPSTRING", "value": "The path to the vcpkg manifest directory."}], "type": "PATH", "value": ""}, {"name": "VCPKG_MANIFEST_INSTALL", "properties": [{"name": "HELPSTRING", "value": "Install the dependencies listed in your manifest:\n    If this is off, you will have to manually install your dependencies.\n    See https://github.com/microsoft/vcpkg/tree/master/docs/specifications/manifests.md for more info.\n"}], "type": "INTERNAL", "value": "OFF"}, {"name": "VCPKG_MANIFEST_MODE", "properties": [{"name": "HELPSTRING", "value": "Use manifest mode, as opposed to classic mode."}], "type": "BOOL", "value": "OFF"}, {"name": "VCPKG_PREFER_SYSTEM_LIBS", "properties": [{"name": "HELPSTRING", "value": "Appends the vcpkg paths to CMAKE_PREFIX_PATH, CMAKE_LIBRARY_PATH and CMAKE_FIND_ROOT_PATH so that vcpkg libraries/packages are found after toolchain/system libraries/packages."}], "type": "BOOL", "value": "OFF"}, {"name": "VCPKG_SETUP_CMAKE_PROGRAM_PATH", "properties": [{"name": "HELPSTRING", "value": "Enable the setup of CMAKE_PROGRAM_PATH to vcpkg paths"}], "type": "BOOL", "value": "ON"}, {"name": "VCPKG_TARGET_TRIPLET", "properties": [{"name": "HELPSTRING", "value": "Vcpkg target triplet (ex. x86-windows)"}], "type": "STRING", "value": "x64-windows"}, {"name": "VCPKG_TRACE_FIND_PACKAGE", "properties": [{"name": "HELPSTRING", "value": "Trace calls to find_package()"}], "type": "BOOL", "value": "OFF"}, {"name": "VCPKG_VERBOSE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Enables messages from the VCPKG toolchain for debugging purposes."}], "type": "BOOL", "value": "OFF"}, {"name": "X_VCPKG_APPLOCAL_DEPS_INSTALL", "properties": [{"name": "HELPSTRING", "value": "(experimental) Automatically copy dependencies into the install target directory for executables. Requires CMake 3.14."}], "type": "BOOL", "value": "OFF"}, {"name": "X_VCPKG_APPLOCAL_DEPS_SERIALIZED", "properties": [{"name": "HELPSTRING", "value": "(experimental) Add USES_TERMINAL to VCPKG_APPLOCAL_DEPS to force serialization."}], "type": "BOOL", "value": "OFF"}, {"name": "Z_VCPKG_BUILTIN_POWERSHELL_PATH", "properties": [{"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "C:/Windows/System32/WindowsPowerShell/v1.0/powershell.exe"}, {"name": "Z_VCPKG_CHECK_MANIFEST_MODE", "properties": [{"name": "HELPSTRING", "value": "Making sure VCPKG_MANIFEST_MODE doesn't change"}], "type": "INTERNAL", "value": "OFF"}, {"name": "Z_VCPKG_POWERSHELL_PATH", "properties": [{"name": "HELPSTRING", "value": "The path to the PowerShell implementation to use."}], "type": "INTERNAL", "value": "C:/Windows/System32/WindowsPowerShell/v1.0/powershell.exe"}, {"name": "Z_VCPKG_PWSH_PATH", "properties": [{"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "Z_VCPKG_PWSH_PATH-NOTFOUND"}, {"name": "Z_VCPKG_ROOT_DIR", "properties": [{"name": "HELPSTRING", "value": "Vcpkg root directory"}], "type": "INTERNAL", "value": "D:/Library/Vc"}, {"name": "_VCPKG_INSTALLED_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory which contains the installed libraries for each triplet"}], "type": "PATH", "value": "D:/Library/Vc/installed"}, {"name": "minimfc_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/Documents/Code/minimfc/build"}, {"name": "minimfc_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "ON"}, {"name": "minimfc_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/Documents/Code/minimfc"}], "kind": "cache", "version": {"major": 2, "minor": 0}}