{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.20"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "minimfc", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-Debug-3a6d81f38eed11a0fc57.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-Debug-98ee960a34d77f1e5665.json", "name": "ZERO_CHECK", "projectIndex": 0}, {"directoryIndex": 0, "id": "minimfc::@6890427a1f51a3e7e1df", "jsonFile": "target-minimfc-Debug-3cd2124a26f5aa40533e.json", "name": "minimfc", "projectIndex": 0}]}, {"directories": [{"build": ".", "jsonFile": "directory-.-Release-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.20"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}], "name": "Release", "projects": [{"directoryIndexes": [0], "name": "minimfc", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-Release-3a6d81f38eed11a0fc57.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-Release-98ee960a34d77f1e5665.json", "name": "ZERO_CHECK", "projectIndex": 0}, {"directoryIndex": 0, "id": "minimfc::@6890427a1f51a3e7e1df", "jsonFile": "target-minimfc-Release-330299cd5f8c2e483683.json", "name": "minimfc", "projectIndex": 0}]}, {"directories": [{"build": ".", "jsonFile": "directory-.-MinSizeRel-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.20"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}], "name": "MinSizeRel", "projects": [{"directoryIndexes": [0], "name": "minimfc", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-MinSizeRel-3a6d81f38eed11a0fc57.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-MinSizeRel-98ee960a34d77f1e5665.json", "name": "ZERO_CHECK", "projectIndex": 0}, {"directoryIndex": 0, "id": "minimfc::@6890427a1f51a3e7e1df", "jsonFile": "target-minimfc-MinSizeRel-2e9c575e4edad77c569a.json", "name": "minimfc", "projectIndex": 0}]}, {"directories": [{"build": ".", "jsonFile": "directory-.-RelWithDebInfo-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.20"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}], "name": "RelWithDebInfo", "projects": [{"directoryIndexes": [0], "name": "minimfc", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-3a6d81f38eed11a0fc57.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-RelWithDebInfo-98ee960a34d77f1e5665.json", "name": "ZERO_CHECK", "projectIndex": 0}, {"directoryIndex": 0, "id": "minimfc::@6890427a1f51a3e7e1df", "jsonFile": "target-minimfc-RelWithDebInfo-9cfe58b51f488b8db746.json", "name": "minimfc", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "D:/Documents/Code/minimfc/build", "source": "D:/Documents/Code/minimfc"}, "version": {"major": 2, "minor": 8}}