^D:\DOCUMENTS\CODE\MINIMFC\BUILD\CMAKEFILES\15110A377270C727F94200A461BFC2E8\GENERATE.STAMP.RULE
setlocal
D:\Library\CMake\bin\cmake.exe -SD:/Documents/Code/minimfc -BD:/Documents/Code/minimfc/build --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file D:/Documents/Code/minimfc/build/minimfc.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
