{"version": 3, "configurePresets": [{"name": "windows-base", "hidden": true, "generator": "Visual Studio 17 2022", "binaryDir": "${sourceDir}/build/${presetName}", "installDir": "${sourceDir}/install/${presetName}", "cacheVariables": {"CMAKE_TOOLCHAIN_FILE": {"type": "FILEPATH", "value": "$env{VCPKG_ROOT}/scripts/buildsystems/vcpkg.cmake"}}, "condition": {"type": "equals", "lhs": "${hostSystemName}", "rhs": "Windows"}}, {"name": "x64-debug", "displayName": "x64 Debug", "inherits": "windows-base", "architecture": {"value": "x64", "strategy": "external"}, "cacheVariables": {"CMAKE_BUILD_TYPE": "Debug"}}, {"name": "x64-release", "displayName": "x64 Release", "inherits": "windows-base", "architecture": {"value": "x64", "strategy": "external"}, "cacheVariables": {"CMAKE_BUILD_TYPE": "Release"}}, {"name": "x86-debug", "displayName": "x86 Debug", "inherits": "windows-base", "architecture": {"value": "Win32", "strategy": "external"}, "cacheVariables": {"CMAKE_BUILD_TYPE": "Debug"}}, {"name": "x86-release", "displayName": "x86 Release", "inherits": "windows-base", "architecture": {"value": "Win32", "strategy": "external"}, "cacheVariables": {"CMAKE_BUILD_TYPE": "Release"}}], "buildPresets": [{"name": "x64-debug", "configurePreset": "x64-debug"}, {"name": "x64-release", "configurePreset": "x64-release"}, {"name": "x86-debug", "configurePreset": "x86-debug"}, {"name": "x86-release", "configurePreset": "x86-release"}]}