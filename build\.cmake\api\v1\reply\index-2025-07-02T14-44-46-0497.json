{"cmake": {"generator": {"multiConfig": true, "name": "Visual Studio 17 2022", "platform": "x64"}, "paths": {"cmake": "D:/Library/CMake/bin/cmake.exe", "cpack": "D:/Library/CMake/bin/cpack.exe", "ctest": "D:/Library/CMake/bin/ctest.exe", "root": "D:/Library/CMake/share/cmake-4.0"}, "version": {"isDirty": false, "major": 4, "minor": 0, "patch": 3, "string": "4.0.3", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-5e235670e68320d8c1b8.json", "kind": "codemodel", "version": {"major": 2, "minor": 8}}, {"jsonFile": "cache-v2-d0455fe1011f4603b5dd.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-51f6da05cce13ea2ad4b.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}, {"jsonFile": "toolchains-v1-b878320978d2a77154ce.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}], "reply": {"client-vscode": {"query.json": {"requests": [{"kind": "cache", "version": 2}, {"kind": "codemodel", "version": 2}, {"kind": "toolchains", "version": 1}, {"kind": "cmakeFiles", "version": 1}], "responses": [{"jsonFile": "cache-v2-d0455fe1011f4603b5dd.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "codemodel-v2-5e235670e68320d8c1b8.json", "kind": "codemodel", "version": {"major": 2, "minor": 8}}, {"jsonFile": "toolchains-v1-b878320978d2a77154ce.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-51f6da05cce13ea2ad4b.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}]}}}}